**AI智能简历助手 - 系统提示词使用与开发说明 (V1)**

**1. 引言**

本说明文档详细阐述了“AI智能简历助手”项目中设计的各个核心系统提示词 (System Prompts) 的用途、调用场景、输入输出规范以及在开发过程中需要注意的事项。本文档旨在帮助开发团队（包括前端、后端及AI集成工程师）准确理解和正确使用这些提示词，以确保AI功能按预期实现，并达到设计的产品质量标准。所有提示词均以直接应用于AI大模型（如Google Gemini 1.5 Pro）为目标进行设计。

**2. 通用调用约定与注意事项**

*   **AI模型：** 本项目V1阶段主要基于 Google Gemini 1.5 Pro API 进行AI交互。
*   **API请求构建：** 后端服务负责根据当前用户操作和上下文，选择合适的系统提示词，并结合用户提供的动态数据（如简历内容、JD文本、用户画像等），构建完整的API请求发送给AI模型。
*   **输入参数的传递：** 提示词中定义的输入变量（如 `exp_userKeywordsInput`, `userContextInfo` 等）需要在API请求中以结构化数据（通常是JSON对象的一部分）的形式传递给AI。提示词本身会说明如何引用这些变量。
*   **响应数据的处理：** 后端服务在接收到AI模型的响应后，需要根据提示词中定义的“响应格式”进行解析。特别是对于JSON格式的输出，必须进行严格的Schema校验和数据类型校验，并处理可能的解析错误。
*   **错误处理与重试机制：** 对于AI API调用失败、超时或返回不符合预期格式的情况，后端应有相应的错误处理逻辑（如记录日志、向前端返回友好提示、在某些情况下可尝试使用简化提示词或不同参数重试）。
*   **Temperature参数：**
    *   对于需要确定性、一致性输出的提示词（如文档解析、诊断报告生成），建议使用较低的 `temperature` 值（例如 0.1 - 0.3）。
    *   对于需要AI进行创造性表达、多样化输出的提示词（如“AI重写此模块”、“不满意？尝试不同风格/重新生成”），可以使用较高的 `temperature` 值（例如 0.7 - 0.9）。
*   **提示词版本管理：** 随着产品的迭代，提示词可能会持续优化。建议在代码或配置中对提示词进行版本化管理。

**3. 系统提示词详解**

以下将逐一介绍项目中设计的核心系统提示词。每个提示词的完整定义（概述、角色设定、任务描述、任务步骤、约束条件、响应格式、示例指导）请参考之前已确认的详细设计文档。本说明将重点强调其**调用场景、核心输入、核心输出及使用方法**。

**3.1 提示词：M1-A - AI优化核心经历描述**

*   **所属模块：** 模块1：AI代写简历
*   **调用场景：** 在模块1的“阶段二：核心内容AI辅助创作与初步优化”中，当用户为某一段核心经历（工作、实习、项目、校园）输入了关键词或简短描述后，点击“AI优化此经历”按钮时调用。
*   **使用方法：** 后端接收前端传递的用户为该经历输入的 `exp_userKeywordsInput` 以及相关的上下文信息（`exp_type`, `profile_experienceStage`, `profile_yearsOfExperience`, `intention_targetPosition`），将这些信息作为参数，结合M1-A系统提示词，调用AI大模型。
*   **核心输入 (由后端组装并传入AI)：**
    *   `exp_userKeywordsInput`: String (用户输入的经历关键词/简述)
    *   `exp_type`: String (经历类型)
    *   `profile_experienceStage`: String (用户职场经验阶段)
    *   `profile_yearsOfExperience`: String (用户工作年限)
    *   `intention_targetPosition`: String (用户目标职位)
*   **核心输出 (AI返回，后端解析后传递给前端)：**
    *   `exp_aiGeneratedDescription`: String (AI优化后的该段核心经历描述文本，通常2-4个bullet points)
*   **前端交互：** 将 `exp_aiGeneratedDescription` 更新到右侧简历预览区的对应经历模块。

**3.2 提示词：LIO-A - 列表信息整理与优化 (技能/奖项)**
    *(原M1-B的功能已整合并强化为此通用提示词)*

*   **所属模块：** 模块1：AI代写简历；模块2、3、4中对技能/奖项部分进行处理时也可借鉴其逻辑。
*   **调用场景 (模块1)：** 在模块1的“阶段二”中，当用户输入了技能列表或荣誉奖项列表后，系统（可由用户点击“AI整理此列表”按钮触发，或在用户完成输入切换焦点时自动触发轻量级处理）调用此提示词。
*   **使用方法：** 后端接收前端传递的用户输入的技能列表 (`userInput_skillsList`) 或奖项列表 (`userInput_awardsList`)，以及可选的上下文信息 (`userContextInfo`)，结合LIO-A系统提示词，调用AI。
*   **核心输入 (处理技能时)：**
    *   `userInput_skillsList`: Array of Objects `[{ skill_name: String, proficiency_level: String }]`
    *   `userContextInfo`: Object (可选，包含 `profile_experienceStage`, `intention_targetPosition`)
*   **核心输出 (处理技能时)：**
    *   `aiFormatted_skillsOutput`: String (Markdown格式的、整理优化后的技能清单文本)
*   **核心输入 (处理奖项时)：**
    *   `userInput_awardsList`: Array of Objects `[{ award_name: String, award_details: String }]`
    *   `userContextInfo`: Object (可选)
*   **核心输出 (处理奖项时)：**
    *   `aiFormatted_awardsOutput`: String (Markdown格式的、整理优化后的荣誉奖项列表文本)
*   **前端交互：** 将 `aiFormatted_skillsOutput` 或 `aiFormatted_awardsOutput` 更新到右侧简历预览区的对应模块。

**3.3 提示词：M1-C - AI代写自我评价/职业目标**

*   **所属模块：** 模块1：AI代写简历
*   **调用场景：** 在模块1的“阶段二”中，当用户在“自我评价/职业目标”模块选择“请求AI代写此部分”并（可选地）输入了核心观点/关键词后调用。
*   **使用方法：** 后端接收前端传递的用户输入的 `summary_userInputKeywords` (可能为空)，并整合用户已填写的全局简历信息（`profile_experienceStage`, `intention_targetPosition`, `coreExperiences`概要, `skills`概要等），将这些信息作为参数，结合M1-C系统提示词，调用AI。
*   **核心输入：**
    *   `summary_userInputKeywords`: String (可选，用户输入的关键词或草稿)
    *   `profile_experienceStage`: String
    *   `profile_yearsOfExperience`: String
    *   `intention_targetIndustry`: String
    *   `intention_targetPosition`: String
    *   `educationEntries_summary`: String (教育背景概要)
    *   `coreExperiences_summary`: String (核心经历概要，特别是成就点)
    *   `skills_summary`: String (核心技能概要)
*   **核心输出：**
    *   `summary_aiGenerated`: String (AI生成的自我评价/职业目标文本初稿)
*   **前端交互：** 将 `summary_aiGenerated` 更新到右侧简历预览区的对应模块。

**3.4 提示词：M1-D - AI整体审阅与最终优化**

*   **所属模块：** 模块1：AI代写简历
*   **调用场景：** 在模块1的“阶段三”中，当用户确认所有模块信息已基本输入/初步优化完毕，点击“AI预览最终简历”按钮时调用。
*   **使用方法：** 后端整合用户当前已填写的全部简历数据 (`full_resume_data_input`)、用户画像、求职意向以及用户（可选的）选择的 `selected_template_id`，将这些信息作为参数，结合M1-D系统提示词，调用AI。
*   **核心输入：**
    *   `full_resume_data_input`: Object/JSON (包含所有已处理的简历模块内容)
    *   `selected_template_id`: String (可选，用户选择的模板ID)
*   **核心输出 (JSON对象)：**
    *   `final_optimized_resume_content`: Object/JSON (其结构与输入类似，但所有文本字段为最终优化版)
    *   `ai_final_review_notes`: String (可选，AI的最终建议或亮点总结)
    *   `recommended_template_id`: String (可选，若用户未选，AI推荐的模板ID)
*   **前端交互：** 使用 `final_optimized_resume_content` 结合（用户选定或AI推荐的）模板，在右侧预览区渲染最终版简历。可选择性展示 `ai_final_review_notes`。

**3.5 提示词：M2-A / M3-A1 / M3-A2 - 文档解析与Markdown化**
    *(这是一个通用型提示词，用于处理用户上传的简历文件)*

*   **所属模块：** 模块2 (基准简历)、模块3 (基准简历和参考简历)
*   **调用场景：** 当用户在这些模块中选择“上传文档”方式导入简历时调用。
*   **使用方法：** 后端接收前端上传的文件对象 (`userInput_resumeFile` 或 `userInput_baseResumeFile` 或 `userInput_refResumeFile`)，将其转换为AI可接受的输入形式，结合此系统提示词，调用AI。
*   **核心输入：**
    *   用户上传的简历文件 (File Object，后端处理为AI可识别格式)
*   **核心输出：**
    *   `baseResume_markdownContent` (或 `referenceResume_markdownContent`): String (Markdown格式的简历内容)
*   **前端交互：** 将输出的Markdown文本加载到对应模块的左侧富文本编辑器中，供用户确认和编辑。

**3.6 提示词：M2-B - 简历诊断与JSON建议输出**

*   **所属模块：** 模块2：AI改写简历
*   **调用场景：** 在模块2的“阶段二”中，当用户确认“基准简历版本”和（可选的）“上下文信息”后，点击“AI诊断简历”按钮时调用。
*   **使用方法：** 后端将 `baseResume_markdownContent` 和 `userContextInfo` 作为参数，结合M2-B系统提示词，调用AI。
*   **核心输入：**
    *   `baseResume_markdownContent`: String (用户确认的基准简历Markdown文本)
    *   `userContextInfo`: Object (可选，用户上下文信息)
*   **核心输出 (JSON数组)：**
    *   `aiDiagnosisReport_json`: Array of Objects (每条建议包含 `diag_markdown_content`, `diag_original_text_for_replacement`, `diag_suggested_text_for_replacement` 等)
*   **前端交互：** 解析 `aiDiagnosisReport_json`，在右上区域以Markdown列表形式展示每条建议的 `diag_markdown_content` (仅供查阅)。同时暂存整个JSON供下一步使用。

**3.7 提示词：M2-C - 结合上下文与诊断结论的简历自动优化**

*   **所属模块：** 模块2：AI改写简历
*   **调用场景：** 在模块2的“阶段三”中，当用户（查阅完诊断报告后）点击“AI最终优化简历”按钮时调用。
*   **使用方法：** 后端将 `baseResume_markdownContent`、`userContextInfo` 以及上一步生成的 `aiDiagnosisReport_json` (或其核心修改指令) 作为参数，结合M2-C系统提示词，调用AI。
*   **核心输入：**
    *   `baseResume_markdownContent`: String
    *   `userContextInfo`: Object (可选)
    *   `aiDiagnosisReport_json`: Array of Objects (或其提炼的核心修改指令)
*   **核心输出 (JSON对象)：**
    *   `aiFinalOptimized_resumeText`: String (Markdown格式的最终优化简历)
    *   `aiFinalOptimization_highlights`: String (可选，优化亮点说明)
*   **前端交互：** 在右下预览区展示 `aiFinalOptimized_resumeText`（套用模板），可选展示 `aiFinalOptimization_highlights`。

**3.8 提示词：M3-B - 参考简历特征与风格深度分析**

*   **所属模块：** 模块3：AI仿写优质简历
*   **调用场景：** 在模块3的“阶段二”中，当用户提供了“参考优质简历”并点击“分析参考简历”按钮后调用。
*   **使用方法：** 后端将 `referenceResume_markdownContent` 作为参数，结合M3-B系统提示词，调用AI。
*   **核心输入：**
    *   `referenceResume_markdownContent`: String (用户确认的参考简历Markdown文本)
*   **核心输出 (JSON对象)：**
    *   `aiReferenceAnalysis_report_json`: Object (详细的、结构化的参考简历特征分析JSON)
    *   `aiReferenceAnalysis_summaryForUser`: String (展示给用户的分析报告摘要文本/Markdown)
*   **前端交互：** 在指定区域展示 `aiReferenceAnalysis_summaryForUser` 供用户确认。暂存 `aiReferenceAnalysis_report_json` 供下一步使用。

**3.9 提示词：M3-C - 基于用户内容与参考特征的智能仿写**

*   **所属模块：** 模块3：AI仿写优质简历
*   **调用场景：** 在模块3的“阶段三”中，当用户确认了“参考简历分析报告摘要”并点击“确认分析，并开始AI仿写”按钮后调用。
*   **使用方法：** 后端将 `userBaseResume_markdownContent`、`aiReferenceAnalysis_report_json` (或其核心指令) 以及可选的 `userContextInfo` 作为参数，结合M3-C系统提示词，调用AI。
*   **核心输入：**
    *   `userBaseResume_markdownContent`: String
    *   `aiReferenceAnalysis_report_json`: Object (或其核心仿写指令)
    *   `userContextInfo`: Object (可选)
*   **核心输出 (JSON对象)：**
    *   `aiImitated_resumeText`: String (Markdown格式的仿写优化简历)
    *   `aiImitation_highlights`: String (可选，仿写亮点说明)
*   **前端交互：** 在右侧预览区展示 `aiImitated_resumeText`（套用模板），可选展示 `aiImitation_highlights`。

**3.10 提示词：M4-A - JD核心要求与关键词深度解析**

*   **所属模块：** 模块4：AI简历精准匹配JD
*   **调用场景：** 在模块4的“阶段一”中，当用户粘贴了JD文本并点击“分析JD”按钮后调用。
*   **使用方法：** 后端将 `userInput_jobDescriptionText` 作为参数，结合M4-A系统提示词，调用AI。
*   **核心输入：**
    *   `userInput_jobDescriptionText`: String (用户输入的JD原文)
*   **核心输出 (JSON对象)：**
    *   `aiJobDescriptionAnalysis_report_json`: Object (详细的、结构化的JD画像分析JSON)
    *   `aiJobDescriptionAnalysis_summaryForUser`: String (展示给用户的JD分析摘要文本/Markdown)
*   **前端交互：** 在指定区域展示 `aiJobDescriptionAnalysis_summaryForUser` 供用户确认。暂存 `aiJobDescriptionAnalysis_report_json` 供下一步使用。

**3.11 提示词：M4-B - 基于用户简历与JD分析的匹配性智能改写**

*   **所属模块：** 模块4：AI简历精准匹配JD
*   **调用场景：** 在模块4的“阶段二”中，当用户确认了JD分析摘要和“用户基准简历”并点击“开始AI匹配并优化简历”按钮后调用。
*   **使用方法：** 后端将 `userBaseResume_markdownContent` 和 `aiJobDescriptionAnalysis_report_json` (或其核心指令) 作为参数，结合M4-B系统提示词，调用AI。
*   **核心输入：**
    *   `userBaseResume_markdownContent`: String
    *   `aiJobDescriptionAnalysis_report_json`: Object (或其核心JD要求指令)
*   **核心输出 (JSON对象)：**
    *   `aiJdMatched_resumeText`: String (Markdown格式的针对JD优化后的简历)
    *   `aiJdMatch_optimizationReport`: String (可选，匹配度与优化说明报告)
*   **前端交互：** 在右侧预览区展示 `aiJdMatched_resumeText`（套用模板），可选展示 `aiJdMatch_optimizationReport`。

**3.12 （待补充设计）提示词：通用 - AI重写指定文本内容 (用于“AI重写此模块/换个说法”)**

*   **所属模块：** 模块1等需要局部内容重写的地方。
*   **调用场景：** 当用户对AI生成的某个具体模块内容（如一段经历描述、一段自我评价）不满意，点击“AI重写此模块”按钮时调用。
*   **核心输入：**
    *   `original_user_input_for_module`: String (用户为该模块提供的原始关键词/简述)
    *   `current_ai_generated_text_for_module`: String (当前用户不满意的、由AI生成的文本)
    *   `relevant_user_context`: Object (相关的用户画像和求职意向信息)
*   **核心输出：**
    *   `rewritten_text_for_module`: String (一个新的、与之前版本表述不同但质量相当的模块文本)
*   **使用方法：** 此提示词的核心在于引导AI在保持原意和质量的基础上，进行**表达方式的多样化和创新**。需要强调“生成一个与[current_ai_generated_text_for_module]不同的版本”。通常配合较高的 `temperature`。



---

