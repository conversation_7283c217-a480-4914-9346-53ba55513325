
---

**产品需求文档 (PRD)**

**项目名称：** AI智能简历助手

---

**1. 产品概述 (Product Overview)**

**1.1 产品愿景 (Product Vision)**
AI智能简历助手致力于成为全球求职者首选的、最值得信赖的简历优化与求职赋能伙伴。我们通过持续创新的AI技术，赋能每一位用户，帮助他们跨越简历写作的障碍，精准展现个人价值，自信地迎接职业生涯中的每一个机遇，最终实现个人职业发展目标。

**1.2 产品目标 (Product Goals)**

*   **核心业务目标：**
    *   为用户提供一站式、自动化的AI简历辅助服务，覆盖从零基础简历创建、现有简历深度优化、优质简历风格借鉴到与目标职位描述（JD）精准匹配的全链路需求。
    *   显著提升用户简历的专业水准、内容质量、表达吸引力及ATS（招聘追踪系统）通过率。
*   **用户体验目标：**
    *   打造极致简洁、操作直观、引导清晰的用户界面，确保用户无需专业指导即可轻松上手并高效完成简历相关任务。
    *   实现AI辅助的智能化、个性化与高效化，让用户在简历准备过程中感受到AI的强大助力而非技术障碍。
    *   确保AI生成与优化结果的专业性、准确性与可信度，赢得用户对产品核心能力的充分信任。
*   **第一期 (V1) 可衡量目标：**
    *   成功上线并稳定运行四大核心功能模块：AI代写简历、AI改写简历、AI仿写优质简历、AI简历精准匹配JD。
    *   用户完成核心功能模块主要流程的平均时长控制在[X分钟]以内（具体数值待产品上线后根据用户行为数据设定和优化，例如：模块1平均15分钟，模块2/3/4平均10分钟）。
    *   核心功能模块的用户任务成功率达到[Y]%以上（例如，用户成功导出优化后简历的比例）。
    *   用户对AI生成/优化结果的满意度（通过后续调研或反馈机制收集）达到[Z]分以上（例如5分制中的4分）。
    *   提供并稳定支持至少3-5套专业、美观的内置简历展示模板。
    *   确保网站在主流桌面浏览器上的兼容性和基本响应式表现。

**1.3 产品范围 (Product Scope - V1)**

*   **核心功能模块 (V1 阶段必须实现)：**
    1.  **模块1：AI代写简历 (From Scratch Creation):**
        *   引导用户输入个人基础信息、教育背景、职场画像、求职意向。
        *   AI辅助用户创作核心经历模块（工作/实习、项目、校园等）的描述内容。
        *   AI辅助用户填写技能清单、荣誉奖项、自我评价/职业目标。
        *   AI对整份简历进行最终的整体审阅与优化排版。
    2.  **模块2：AI改写简历 (Revision & Optimization):**
        *   支持用户上传/粘贴现有简历内容。
        *   AI进行文档解析与Markdown格式化预处理。
        *   引导用户补充求职上下文信息（推荐）。
        *   AI进行三步式处理：
            *   **识别：** 用户确认基准简历。
            *   **诊断：** AI分析简历问题并生成详细的“诊断报告”（JSON格式，前端渲染为Markdown列表供用户查阅）。
            *   **优化：** AI自动采纳诊断结论，并结合用户上下文，对简历进行全面优化，生成最终版本。
    3.  **模块3：AI仿写优质简历 (Imitation of Exemplars):**
        *   用户提供自身的“基准简历”和一份“参考优质简历”范例。
        *   引导用户补充求职上下文信息（推荐）。
        *   AI分析“参考优质简历”的结构、风格、表达等特征，并向用户展示分析报告摘要供确认。
        *   用户确认后，AI将参考简历的优点智能地“嫁接”或“适配”到用户基准简历上，生成仿写优化版。
    4.  **模块4：AI简历精准匹配JD (JD-Specific Tailoring):**
        *   用户提供目标职位的职位描述（JD）文本。
        *   AI深度解析JD，提取核心要求与关键词，并向用户展示JD分析摘要供确认。
        *   用户提供自身的“基准简历”。
        *   用户确认后，AI将用户简历与JD要求进行全面对比，并进行针对性的智能改写与优化，以最大化匹配度。
*   **支撑功能 (V1 阶段必须实现)：**
    *   **内置简历展示模板：** 系统内置3-5套专业、简洁的简历展示模板，用户可在预览和导出时选择应用。
    *   **简历导出功能：** 支持将最终简历导出为PDF（首选，保持格式）、Word（.docx，方便再编辑）、TXT（纯文本）三种格式。
    *   **客户端临时数据存储/会话级简历传递机制：** 在用户未登录的情况下，支持在当前会话或浏览器本地临时存储用户正在处理的简历数据，以便在不同模块间或单模块多步骤间进行有限度的内容传递和复用，提升单次使用体验的连贯性。（需明确告知用户此为临时存储，关闭浏览器或清除缓存可能丢失数据）。
*   **V1 阶段明确不包含的功能：**
    *   用户账户系统（注册、登录、密码找回、用户数据持久化存储）。
    *   用户简历库管理功能（保存多份简历到云端、从云端加载简历等）。
    *   任何形式的付费功能或会员体系。
    *   平台自建的、用户可浏览和管理的“简历模板库”功能模块。
    *   团队协作、简历分享链接等社交功能。
    *   简历投递、面试追踪等后续求职管理功能。
    *   运营后台管理系统。

**2. 目标用户画像 (Target User Persona)**

*   **2.1 应届毕业生/在校生 (The Recent Graduate / Student)**
    *   **人口统计学特征（示例）：** 年龄18-24岁，在读或刚毕业，求职经验少或无。
    *   **技术使用习惯：** 熟练使用互联网和各类在线工具，对AI技术有较高接受度和好奇心。
    *   **核心需求与痛点：**
        *   **简历内容匮乏：** 缺乏实际工作经验，不知道如何挖掘和呈现校园经历（课程项目、实习、社团活动、志愿者等）的价值。
        *   **不了解求职规范：** 对标准简历的结构、内容侧重、专业表达方式不熟悉。
        *   **表达能力不足：** 难以用精炼、有力的语言描述自己的能力和潜力。
        *   **时间压力：** 可能同时面临毕业论文、考试和求职等多重压力，希望快速高效地准备简历。
        *   **缺乏自信：** 对自己写出的简历不自信，担心无法通过筛选。
    *   **使用本产品的期望：**
        *   获得清晰的指引，从零开始构建一份结构完整、内容充实的简历。
        *   AI能够帮助他们将模糊的经历描述得更专业、更具吸引力。
        *   快速生成一份符合目标行业/职位初步要求的简历，作为求职的“敲门砖”。
        *   通过AI辅助，提升简历的整体质量，增加获得面试机会的可能性。

*   **2.2 职场新人/经验不足者 (The Early Career Professional / Less Experienced Individual)**
    *   **人口统计学特征（示例）：** 年龄22-28岁，通常拥有0-3年工作经验，可能处于职业生涯早期或寻求职业转型初期。
    *   **技术使用习惯：** 习惯使用在线工具提升工作效率，对能解决实际问题的AI应用感兴趣。
    *   **核心需求与痛点：**
        *   **简历内容优化困难：** 已有简历，但内容可能较为平淡，缺乏亮点，不懂如何进行有效优化。
        *   **成就总结与量化能力弱：** 难以将日常工作职责提炼为有说服力的成就，不擅长用数据和结果来证明自己的价值。
        *   **表达不够专业或不具针对性：** 语言表达可能不够精炼、职业化，或者简历内容与特定目标职位匹配度不高。
        *   **缺乏修改方向和方法：** 知道简历需要改进，但不知道从何处下手，或者如何改写才能更好。
        *   **希望提升竞争力：** 意识到简历是求职竞争的关键，希望通过优化简历获得更好的职业机会。
    *   **使用本产品的期望：**
        *   AI能够对现有简历进行深度诊断，并给出具体、可行的优化方案。
        *   AI帮助润色语言，强化成就描述，提升简历的专业度和说服力。
        *   AI辅助调整简历内容，使其更贴合目标职位的要求。
        *   通过AI的帮助，快速将现有简历提升一个档次，增强求职竞争力。

*   **2.3 共同特征 (All Target Users)**
    *   **时间敏感性：** 通常在积极求职期间使用，希望工具能够快速产出有效成果，节省时间。
    *   **结果导向：** 使用工具的最终目的是提升简历质量，从而获得更多面试和工作机会。
    *   **对“简单易用”的偏好：** 更倾向于操作流程直观、引导清晰、“傻瓜化”的工具，不希望花费过多时间学习复杂功能。
    *   **对AI能力的期待与依赖：** 相信AI技术能够提供有价值的辅助，并愿意在一定程度上依赖AI的建议和生成结果。
    *   **求职焦虑与信息不对称：** 可能都面临求职压力，对如何撰写一份能打动招聘方的简历存在信息获取和判断上的不足。

---

---

**首页 - 详细功能需求与设计方案 (V1 MVP)**

**1. 页面目标与核心价值传递**

*   **页面ID：** Homepage
*   **页面入口：** 用户访问网站根域名时的默认着陆页。
*   **核心目标：**
    1.  **清晰传达产品核心价值：** 用户一进入首页，就能快速理解这是一个通过AI技术帮助他们高效创建和优化专业简历的在线工具。
    2.  **直接引导至核心功能：** 提供最直接、最醒目的入口，引导用户根据自身需求快速开始使用四大核心简历处理功能模块。
    3.  **建立初步信任感：** 通过简洁、专业、现代的视觉设计和清晰的价值主张，使用户产生信任并愿意尝试使用产品。
*   **V1阶段不承载的目标：** 用户注册/登录、付费引导、复杂的社区或内容运营入口。

**2. 整体布局与UI设计原则**

*   **2.1 布局类型：** 单屏或短滚动页面，采用响应式设计，优先保证桌面端良好体验，移动端能基本可用。
*   **2.2 UI设计原则：**
    *   **专业感与信任感：** 视觉风格应体现专业性、可靠性，色彩运用克制（例如，使用科技蓝、商务灰作为主色调，辅以明亮的强调色用于CTA按钮），字体选择清晰易读。
    *   **简洁明了：** 避免信息过载，突出核心价值和操作入口，留有足够的视觉呼吸空间。
    *   **引导性强：** 通过视觉层级（大小、对比、位置）、色彩、动效（轻微的鼠标悬停效果）等引导用户视线和操作。
    *   **加载速度快：** 优化图片资源，避免使用过多或过重的JS动画，确保首页快速加载。

**3. 页面核心元素、功能、交互与提示**

    *   **3.1 导航栏 (Header)**
        *   **功能描述：** 提供网站Logo/品牌标识和指向四大核心功能模块的快速导航链接。
        *   **布局与样式：** 通常位于页面顶部，可设计为固定导航栏 (sticky header)，在页面滚动时保持可见。背景色与页面主体有适当区分。
        *   **元素：**
            *   **左侧：**
                *   `site_logo`: 图片Logo 或 `site_product_name`: 文本形式的产品名称 (例如：“AI简历助手”)。
                *   **交互：** 点击Logo/产品名称，通常链接回首页本身。
            *   **右侧 (导航链接列表)：**
                *   `nav_link_module1`: 文本链接“AI代写”，点击后跳转至模块1（AI代写简历）的主界面。
                *   `nav_link_module2`: 文本链接“AI改写”，点击后跳转至模块2（AI改写简历）的主界面。
                *   `nav_link_module3`: 文本链接“AI仿写”，点击后跳转至模块3（AI仿写优质简历）的主界面。
                *   `nav_link_module4`: 文本链接“AI匹配JD”，点击后跳转至模块4（AI简历精准匹配JD）的主界面。
                *   **UI设计：** 链接文本清晰易懂，间距适中。鼠标悬停时有视觉反馈（如下划线、颜色变化）。当前所在模块（如果用户已进入某模块）的导航链接应有高亮激活状态（但首页本身无此状态）。
        *   **字段：** 无需用户输入的动态字段。链接地址为固定内部路由。
        *   **用户提示文本示例：** 导航链接文本本身即为提示。

    *   **3.2 英雄区/主视觉区 (Hero Section)**
        *   **功能描述：** 占据首屏核心位置，通过强有力的价值主张和行动召唤，吸引用户注意力并引导其开始使用产品。
        *   **布局与样式：** 通常为通栏设计，背景可以是高质量的抽象图片、渐变色或与产品主题相关的简洁插画，避免过于复杂分散注意力。内容元素（标题、副标题、CTA按钮）垂直居中或视觉重心对齐。
        *   **元素：**
            *   `hero_main_headline` (H1 大标题):
                *   **内容示例：** “AI赋能，简历升级，Offer加倍！” 或 “智能简历引擎，秒速打造专业求职利器”
                *   **UI设计：** 页面上最大、最醒目的文本，字体选择现代、专业，颜色与背景对比强烈。
            *   `hero_sub_headline` (P 段落，副标题/描述):
                *   **内容示例：** “AI智能简历助手，从创建、优化到精准匹配，一站式解决您的所有简历难题，让您的求职之路更平坦高效。”
                *   **UI设计：** 位于主标题下方，字体小于主标题，颜色稍柔和，文本行长适中以保证可读性。
            *   `hero_primary_cta_button` (主要行动召唤按钮):
                *   **功能描述：** 引导用户进入功能选择流程或最核心的功能模块。
                *   **按钮文本示例：** “**开始提升我的简历**” 或 “**免费智能创建/优化简历**”
                *   **交互：** 用户点击此按钮后，**触发一个模态框（Modal）弹出**，模态框内提供四大核心功能模块的选择入口。
                *   **UI设计：** 按钮设计应极具吸引力，尺寸较大，颜色使用品牌强调色，有清晰的鼠标悬停和点击动效。
            *   **模态框 (Modal for Module Selection)：**
                *   **触发：** 点击 `hero_primary_cta_button` 后弹出。
                *   **标题：** “请选择您需要的服务：” 或 “您想如何开始？”
                *   **选项 (以按钮或带图标的链接列表形式呈现，每个选项包含模块名称和简短描述)：**
                    1.  `modal_option_module1`: “**AI代写简历** - 从零开始，AI助您快速创建一份专业新简历。” -> 点击跳转模块1
                    2.  `modal_option_module2`: “**AI改写简历** - 上传现有简历，AI深度诊断并全面优化。” -> 点击跳转模块2
                    3.  `modal_option_module3`: “**AI仿写优质简历** - 学习高手范例，AI帮您借鉴优点，提升格调。” -> 点击跳转模块3
                    4.  `modal_option_module4`: “**AI简历精准匹配JD** - 针对职位要求，AI智能优化，让简历更契合。” -> 点击跳转模块4
                *   **UI设计：** 模态框设计简洁，选项清晰易懂，易于点击和关闭（提供关闭按钮或点击外部区域关闭）。
        *   **字段：** 无需用户输入的动态字段。
        *   **用户提示文本示例：** (已包含在内容示例中)

    *   **3.3 (可选，但推荐) 功能模块简介与直接入口区 (Features Section)**
        *   **功能描述：** 在英雄区下方，以更详细的方式介绍四大核心功能模块的价值和入口，作为对英雄区CTA模态框选择的补充或替代路径。
        *   **布局与样式：** 通常采用2x2的卡片网格布局（桌面端），或垂直堆叠的区块（移动端）。每个卡片/区块对应一个功能模块。
        *   **每个卡片/区块包含：**
            *   `feature_icon_moduleX` (可选): 与模块功能相关的、简洁明了的扁平化或线性图标。
            *   `feature_title_moduleX` (H3或H4标题): 模块名称，例如：“AI代写简历”。
            *   `feature_description_moduleX` (短文本): 1-2句话概括模块的核心价值和能为用户解决的问题。例如：“无论您是求职新手还是希望简历焕然一新，AI都能引导您轻松完成专业简历的第一稿。”
            *   `feature_cta_button_moduleX` (“了解更多”或“立即使用”按钮/链接): 点击后直接跳转到对应模块的主界面。
        *   **UI设计：** 卡片/区块设计应简洁、视觉统一，信息层级清晰，CTA按钮明确。可使用轻微的鼠标悬停效果增强交互感。
        *   **字段：** 无需用户输入的动态字段。
        *   **用户提示文本示例：** (已包含在内容示例中)

    *   **3.4 (可选，极简V1可省略) 产品优势/特点展示 (Benefits Section)**
        *   **功能描述：** 简要突出产品的核心竞争优势或用户可获得的主要益处。
        *   **布局与样式：** 通常在功能模块区之后，采用横向排列的3-4个“图标+标题+简短描述”的组合。
        *   **内容示例 (每个优势点)：**
            *   图标1 + “**AI智能驱动**” + “依托先进AI技术，深度理解您的需求，提供个性化智能辅助。”
            *   图标2 + “**专业高效产出**” + “快速生成与优化专业级简历，显著节省您的宝贵时间与精力。”
            *   图标3 + “**完全免费体验 (V1)**” + “所有核心功能均免费开放，助您无忧求职。” (强调V1免费)
        *   **UI设计：** 布局均衡，图标风格统一，文字简洁有力。
        *   **字段：** 无需用户输入的动态字段。

    *   **3.5 页脚 (Footer)**
        *   **功能描述：** 提供网站版权信息和一些可选的辅助链接。
        *   **布局与样式：**位于页面最底部，通栏设计，背景色通常较深或与主内容区有明显区分，文字颜色较浅。
        *   **元素：**
            *   `footer_copyright`: 文本：“© [当前年份] [AI智能简历助手 或 您的产品/团队名称]. All Rights Reserved.”
            *   （可选V1，若有内容则添加）`footer_link_about_us`: 文本链接“关于我们” -> 跳转至关于我们页面。
            *   （可选V1）`footer_link_help_faq`: 文本链接“帮助中心/FAQ” -> 跳转至帮助页面。
            *   （可选V1）`footer_link_privacy_policy`: 文本链接“隐私政策” -> 跳转至隐私政策页面。
            *   （可选V1）`footer_link_terms_of_service`: 文本链接“服务条款” -> 跳转至服务条款页面。
        *   **UI设计：** 简洁，字体不宜过大，链接清晰可点。
        *   **字段：** 无需用户输入的动态字段。

---


---

**模块1：AI代写简历 - 详细功能需求**

**1. 功能模块总览**

*   **模块名称：** AI代写简历
*   **模块ID：** M1
*   **模块入口：** 网站主导航栏“AI代写简历”链接/按钮，或首页引导入口。
*   **核心目标：** 引导用户从零开始，通过分阶段的信息输入和AI的智能辅助，快速高效地创建一份结构完整、内容专业、初步符合求职需求的简历初稿。

**2. 整体界面与交互原则**

*   **2.1 界面布局：**
    *   **描述：** 采用左右分栏的响应式布局。
        *   **左侧区域：** 信息录入与交互区。包含动态表单、选项卡/步骤引导、操作按钮。
        *   **右侧区域：** AI生成结果实时/准实时预览与交付区。以结构化、格式化的标准简历样式展示内容，应用用户选定或系统默认的内置模板。
    *   **交互：** 用户在左侧输入信息或触发AI操作，右侧预览区相应更新。

*   **2.2 交互原则：**
    *   **引导清晰：** 通过明确的标题、提示文本、步骤指示，引导用户逐步完成信息输入。
    *   **即时反馈：** 用户输入固化信息后，右侧预览区应实时更新；AI处理核心模块内容后，预览区也应及时更新。AI处理期间有加载提示。
    *   **用户可控：** 用户对AI生成的核心内容拥有请求重写和手动微调的权限。
    *   **简洁高效：** 避免不必要的操作步骤，尽可能简化用户输入负担。

**3. 详细功能点、字段、流程与交互**

**3.1 阶段一：基础信息与框架构建**

    *   **3.1.1 功能点：个人联系方式与基本画像录入**
        *   **功能描述：** 收集用户的基本联系信息和求职相关的辅助画像信息。
        *   **用户流程：**
            1.  用户进入模块1，界面展示此部分的输入表单。
            2.  用户填写各项信息。
            3.  用户输入的信息实时或在失焦后更新到右侧预览区的“个人信息”模块（按模板格式化展示）。
        *   **交互设计：**
            *   左侧提供清晰的文本输入框和下拉选择器。
            *   输入框配有占位符提示和标签说明。
            *   电话、邮箱字段进行前端实时格式校验，错误时给出文字提示。
            *   “期望城市”支持多选，可采用标签输入或多选下拉框。
        *   **关键字段：**
            *   `personalInfo_name` (姓名): String, 必填
            *   `personalInfo_phone` (电话): String, 必填, 格式: 手机号
            *   `personalInfo_email` (邮箱): String, 必填, 格式: 邮箱
            *   `personalInfo_targetCities`: Array of Strings, 可选 (UI强引导), 示例: ["北京", "上海"]
            *   `personalInfo_jobSeekingStatus`: String (Enum: "积极找工作", "随便看看机会", "暂不考虑"), 可选
        *   **用户提示文本示例：**
            *   姓名输入框标签：“姓名”
            *   电话输入框占位符：“请输入您的手机号码”
            *   邮箱格式错误提示：“请输入有效的邮箱地址”
            *   期望城市引导：“填写期望城市，有助于AI后续推荐（若有此功能）或内容调整。”

    *   **3.1.2 功能点：教育背景信息录入**
        *   **功能描述：** 收集用户的教育经历，支持添加多条记录。
        *   **用户流程：**
            1.  用户填写第一条教育经历的各项信息。
            2.  用户输入的信息实时或在失焦后更新到右侧预览区的“教育背景”模块。
            3.  用户可点击“添加更多教育经历”按钮，动态增加一组新的教育经历表单。
            4.  用户可点击某条已填写经历旁的“删除”按钮移除该条记录。
        *   **交互设计：**
            *   左侧每条教育经历以卡片或表单组形式展示。
            *   提供清晰的“添加更多教育经历”和“删除此条经历”按钮。
            *   “在校时间”使用起止年月选择器。
        *   **关键字段 (教育背景数组 `educationEntries`，每个对象包含)：**
            *   `edu_id`: String, 唯一ID (前端生成或后端生成)
            *   `edu_schoolName`: String, 必填
            *   `edu_degree`: String (Enum: "大专", "本科", "硕士", "博士", "高中", "其他"), 必填
            *   `edu_major`: String, 必填
            *   `edu_startDate`: String (YYYY-MM), 必填
            *   `edu_endDate`: String (YYYY-MM 或 "至今"), 必填
            *   `edu_gpa`: String, 可选, 示例: "3.8/4.0" 或 "专业前10%"
            *   `edu_courses_input`: String (多行文本，用户输入与求职意向相关的核心课程关键词或简述), 可选
            *   `edu_courses_ai_formatted`: String (AI辅助将 `edu_courses_input` 整理成的列表文本，用于预览), 可选
        *   **用户提示文本示例：**
            *   学历下拉框默认选项：“请选择学历”
            *   主修课程输入框提示：“请填写与目标职位相关的3-5门核心课程（可选）”
            *   添加按钮文本：“+ 添加教育经历”

    *   **3.1.3 功能点：职场画像定位与求职意向设定**
        *   **功能描述：** 收集用户的职场经验阶段、工作年限以及求职目标，这些信息主要供AI后续进行内容生成和优化决策，部分信息会展示在简历的“求职意向”部分。
        *   **用户流程：**
            1.  用户在左侧表单区依次选择或填写相关信息。
            2.  “目标行业”和“目标职位”等信息会更新到右侧预览区的“求职意向”模块（若模板中有此模块）。
        *   **交互设计：**
            *   “职场经验阶段”、“工作年限”、“目标行业”、“目标职位/职能”、“工作类型”均使用下拉选择器，提供预设选项。
            *   “目标行业”和“目标职位/职能”可设计为二级联动下拉，或支持关键词搜索预设库。
            *   允许用户在“目标职位/职能”选择预设库内容外，手动输入自定义的精确职位名称。
        *   **关键字段：**
            *   `profile_experienceStage`: String (Enum: "在校生/实习生", "应届毕业生", "职场人士"), 必填
            *   `profile_yearsOfExperience`: String (Enum: "0年 (在校)", "1年以内", "1-3年", "3-5年", "5-10年", "10年以上"), 必填
            *   `intention_targetIndustry`: String, 必填 (来自预设行业库)
            *   `intention_targetPosition`: String, 必填 (来自预设职位库或用户自定义)
            *   `intention_expectedSalary`: String, 可选 (范围或文本)
            *   `intention_jobType`: String (Enum: "全职", "实习", "兼职"), 可选
        *   **用户提示文本示例：**
            *   职场经验阶段引导：“选择您当前的经验阶段，AI将据此优化简历内容。”
            *   目标职位输入框提示：“请选择或输入您的目标职位，如“产品经理”。”

**3.2 阶段二：核心内容AI辅助创作与初步优化**

    *   **3.2.1 功能点：核心经历模块动态引导与AI初步优化**
        *   **功能描述：** 系统根据用户选择的“职场经验阶段”，动态展示引导用户填写的核心经历模块类型（如工作/实习经历、项目经历、校园经历）。用户为每个经历模块输入关键要素后，可请求AI对该模块内容进行初步的扩写、润色和专业化表达。
        *   **用户流程：**
            1.  左侧输入区根据 `profile_experienceStage` 动态显示当前应填写的核心经历模块A（例如“工作经历”）。
            2.  用户填写模块A的各项关键信息（机构名称、职位/角色、时间、核心职责/成就的关键词或简短描述）。
            3.  用户点击模块A旁的“**AI优化此经历**”按钮。
            4.  **AI处理：** 后端调用针对该经历类型的AI提示词（提示词M1-A系列），对用户输入进行初步优化。
            5.  右侧预览区中，模块A的内容更新为AI优化后的版本。
            6.  用户可选择点击模块A预览旁的“**AI重写此经历**”按钮，或在右侧预览区直接手动修改AI生成的内容（如果预览区支持富文本编辑并能同步数据）。
            7.  用户完成模块A后，系统引导或用户手动切换到下一个核心经历模块B，重复步骤2-6。
            8.  用户完成所有被引导的核心经历模块。
        *   **交互设计：**
            *   左侧核心经历模块以可折叠面板、选项卡或顺序步骤的形式呈现，一次聚焦一个模块的填写。
            *   每个经历模块表单包含清晰的输入字段和引导提示。
            *   “AI优化此经历”按钮醒目，点击后按钮状态变为“处理中…”并伴有加载动画。
            *   右侧预览区对应模块内容更新时，可以有高亮或动画效果提示用户。
            *   “AI重写此经历”按钮位于右侧预览区该模块旁。
        *   **关键字段 (核心经历数组 `coreExperiences`，每个对象包含)：**
            *   `exp_id`: String, 唯一ID
            *   `exp_type`: String (Enum: "工作经历", "实习经历", "项目经历", "校园经历")
            *   `exp_organizationName`: String, 必填
            *   `exp_positionOrRole`: String, 必填
            *   `exp_startDate`: String (YYYY-MM), 必填
            *   `exp_endDate`: String (YYYY-MM 或 "至今"), 必填
            *   `exp_userKeywordsInput`: String (多行文本，用户为该经历提供的核心职责/成就关键词或简述), 必填
            *   `exp_aiGeneratedDescription`: String (AI初步优化后的描述文本，用于预览和最终生成，用户可再编辑)
        *   **用户提示文本示例：**
            *   工作经历关键词输入提示：“请简述您在这段工作中的主要职责和取得的关键成就（2-4点），AI将为您优化表达。”
            *   “AI优化此经历”按钮文本。
            *   “AI重写此经历”按钮文本或图标。
            *   AI处理成功提示：“「[经历名称]」已优化！”

    *   **3.2.2 功能点：其他关键内容模块AI辅助填写 (技能、奖项、自我评价)**
        *   **功能描述：** 引导用户填写技能清单、荣誉奖项、自我评价/职业目标等模块，AI提供辅助整理、格式化或代写服务。
        *   **用户流程：**
            1.  用户在左侧选择或系统引导至“技能清单”模块。
            2.  用户输入技能名称和熟练程度（可多条）。AI（提示词M1-B）可辅助进行分类整理和表达规范化，结果更新到右侧预览。
            3.  用户切换至“荣誉奖项”模块，输入奖项信息（可多条）。AI（提示词M1-B）辅助进行格式化呈现，结果更新到右侧预览。
            4.  用户切换至“自我评价/职业目标”模块。
                *   **选项1：用户自行输入完整内容。** 右侧预览区直接展示。
                *   **选项2：用户输入核心观点/关键词，点击“请求AI代写此部分”按钮。** AI（提示词M1-C）结合用户全局信息生成初稿，更新到右侧预览。用户可请求“AI重写此部分”。
        *   **交互设计：**
            *   各模块在左侧有清晰的入口/表单。
            *   技能/奖项支持动态增删条目。
            *   自我评价提供文本区域和“请求AI代写”按钮。
            *   AI处理结果均在右侧预览区对应模块更新。
        *   **关键字段：**
            *   `skills`: Array of Objects [{ `skill_id`: String, `skill_name`: String, `skill_proficiency`: String (Enum), `skill_category_ai`: String (可选) }]
            *   `awards`: Array of Objects [{ `award_id`: String, `award_name`: String, `award_details`: String (如时间/级别) }]
            *   `summary_userInput`: String (用户自行输入的自我评价或给AI的关键词)
            *   `summary_aiGenerated`: String (AI代写或优化的自我评价文本，用户可再编辑)
        *   **用户提示文本示例：**
            *   技能熟练度选项：“了解、应用、熟练、精通”
            *   “请求AI代写此部分”按钮文本。
            *   自我评价输入提示：“您可以简述您的核心优势和职业目标，或直接让AI为您生成。”

**3.3 阶段三：AI整体审阅优化与终稿生成**

    *   **3.3.1 功能点：AI整体审阅与最终优化**
        *   **功能描述：** 用户在完成所有信息输入和各模块初步优化后，触发AI对整份简历进行一次全面的最终审阅、内容连贯性与风格统一性调整、ATS友好性检查、以及结合选定模板进行专业排版。
        *   **用户流程：**
            1.  用户确认所有模块信息已基本输入/初步优化完毕。
            2.  用户点击“**AI预览最终简历**”按钮。
            3.  **AI处理：** 后端调用“整体优化”AI提示词（提示词M1-D），输入为用户已填写的全部简历数据、用户画像、求职意向及当前选定的模板ID。
            4.  右侧预览区更新为AI整体优化后的完整简历终稿。
        *   **交互设计：**
            *   “AI预览最终简历”按钮在用户完成主要内容填写后，在左侧或全局操作区域变得醒目可用。
            *   点击后，按钮状态变为“AI正在生成最终简历…”并伴有加载动画。
            *   生成成功后，右侧预览区刷新，并可有toast提示“最终简历已生成！”
        *   **关键字段：** 无新增核心数据字段，此步骤是对已有数据进行整合和最终渲染。
        *   **用户提示文本示例：**
            *   “AI预览最终简历”按钮文本。
            *   加载提示：“AI正在对您的简历进行全面优化和排版，这可能需要一点时间，请耐心等待…”
            *   成功提示：“最终版简历已生成！请在右侧预览，您还可以选择不同模板或进行微调。”

    *   **3.3.2 功能点：最终预览、模板选择与用户微调**
        *   **功能描述：** 用户查看AI生成的最终简历，可以选择更换内置模板，并对文本内容进行所见即所得的微调。
        *   **用户流程：**
            1.  右侧预览区展示AI整体优化后的最终简历。
            2.  用户通过模板选择器更换不同的内置简历模板，预览区实时应用新模板样式。
            3.  用户直接在右侧预览区的文本上进行点击、修改、删除、添加等微调操作。
        *   **交互设计：**
            *   右侧预览区上方或旁边提供清晰的内置简历模板选择器（如带缩略图）。
            *   预览区文本支持富文本编辑功能，编辑操作应流畅自然。
            *   （可选）如果某模块内容在整体优化后仍不满意，可以保留该模块旁的“AI重写此模块”按钮，此时重写是基于已整体优化的版本。
        *   **关键字段：**
            *   `final_resume_content_html_or_structured_text`: String (经过用户微调后的、最终用于导出的简历内容，格式取决于预览区的实现)
            *   `final_selected_template_id`: String (用户最终选定的模板ID)
        *   **用户提示文本示例：**
            *   模板选择器提示：“尝试不同简历风格？”
            *   微调引导（首次）：“您可以直接在右侧预览区点击修改文本内容。”

    *   **3.3.3 功能点：保存与导出简历**
        *   **功能描述：** 用户将最终确认的简历保存到账户（若已登录）并/或导出为本地文件。
        *   **用户流程：**
            1.  用户对最终预览结果满意。
            2.  （若已登录）点击“保存简历”按钮，简历数据被保存到用户简历库。
            3.  用户点击“导出简历”按钮。
            4.  系统弹出格式选择对话框（PDF、Word、TXT）。
            5.  用户选择格式并确认，系统生成相应文件供用户下载。
        *   **交互设计：**
            *   “保存简历”和“导出简历”按钮位于右侧预览区上方或全局操作栏。
            *   导出格式选择清晰明了。
        *   **关键字段：** (参考通用功能中的简历库字段和导出设置)
        *   **用户提示文本示例：**
            *   保存成功：“简历已成功保存！”
            *   导出格式选择：“请选择您要导出的文件格式：”
            *   文件生成提示：“正在生成[格式]文件，请稍候…”

---



---

**模块2：AI改写简历 (三步式：识别->诊断->优化) - 详细功能需求**

**1. 功能模块总览**

*   **模块名称：** AI改写简历
*   **模块ID：** M2
*   **模块入口：** 网站主导航栏“AI改写”链接/按钮，或首页引导入口。
*   **核心目标：** 帮助已有简历初稿的用户，通过AI的深度诊断（展示给用户参考）和智能化的自动全面优化，显著提升简历的专业性、表达力及与目标岗位的匹配度，最终交付一份全面升级的简历版本。

**2. 整体界面与交互原则**

*   **2.1 界面布局：**
    *   **描述：** 采用“优化的左右分栏”布局。
        *   **左侧区域：** 用户原始简历内容导入/编辑区 + （推荐的）用户上下文信息输入区。
        *   **右侧区域：** 根据当前流程步骤动态展示内容。
            *   **诊断阶段：** 右上展示AI的“简历诊断报告”（只读Markdown列表）。
            *   **优化阶段：** 右下展示AI最终优化后的简历全文预览。右上可展示“优化亮点说明”。
    *   **交互：** 用户在左侧完成输入和触发操作，右侧相应区域展示AI的处理结果。

*   **2.2 交互原则：**
    *   **分步清晰：** 通过明确的按钮和界面状态，引导用户完成“识别->诊断->优化”三个主要步骤。
    *   **信息透明（诊断阶段）：** AI的诊断分析过程和建议对用户可见，增强理解和信任。
    *   **操作简化（优化阶段）：** AI自动应用优化，用户无需逐条决策。
    *   **结果可控：** 用户对AI最终生成的优化版简历拥有微调权和请求“再优化一次”的权利。

**3. 详细功能点、字段、流程与交互**

**3.1 阶段一：输入与准备 (用户操作)**

    *   **3.1.1 功能点：简历内容导入与基准版本确认**
        *   **功能描述：** 用户通过粘贴文本或上传文档的方式导入其现有简历。系统（AI辅助）对上传的文档进行解析和Markdown格式化处理，用户在富文本编辑器中对导入内容进行最终确认，形成“基准简历版本”。
        *   **用户流程：**
            1.  用户进入“AI改写简历”模块。
            2.  左侧内容导入区提示用户选择导入方式。
            3.  **若选择“粘贴文本”：** 用户将简历内容粘贴到支持Markdown的富文本编辑器中。
            4.  **若选择“上传文档” (.doc, .docx, .pdf)：** 用户上传文件。后端调用AI（提示词M2-A）进行文档解析与Markdown化，结果回显到左侧富文本编辑器。
            5.  用户在富文本编辑器中审阅并（可）编辑AI初步处理（或直接粘贴）的内容，最终确认形成“**基准简历版本**”。
        *   **交互设计：**
            *   左侧提供清晰的“粘贴简历内容”文本区域和“上传简历文件”按钮。
            *   上传文档后，有加载提示，处理完成后Markdown内容自动填充到编辑器。
            *   富文本编辑器提供基础的Markdown编辑和预览功能。
        *   **关键字段：**
            *   `userInput_resumeFile`: File Object (用户上传的文件，临时)
            *   `userInput_resumePastedText`: String (用户粘贴的文本)
            *   `baseResume_markdownContent`: String (用户最终确认的、用于后续分析的基准简历Markdown文本)
        *   **用户提示文本示例：**
            *   导入区标题：“请提供您需要优化的简历”
            *   粘贴区占位符：“在此粘贴您的简历内容…”
            *   上传按钮文本：“上传简历文件 (Word/PDF)”
            *   AI解析提示：“AI正在解析您的文档并进行格式化，请稍候…”
            *   编辑器上方提示：“请在此确认或编辑您的简历内容，这将作为AI分析和优化的基础。”

    *   **3.1.2 功能点：用户上下文信息补充 (推荐)**
        *   **功能描述：** 引导用户提供其求职意向（目标行业、职位等）和职场画像（经验阶段、工作年限）等上下文信息，这些信息将用于AI在诊断和最终优化阶段提供更具针对性的服务。
        *   **用户流程：**
            1.  在用户确认“基准简历版本”后（或并行），左侧出现上下文信息填写表单。
            2.  用户填写各项推荐信息。这些信息将被暂存，用于后续AI处理。
        *   **交互设计：**
            *   表单形式，包含下拉选择器和文本输入框。
            *   清晰标注为“推荐填写”，并简要说明填写的好处。
        *   **关键字段 (存储为 `userContextInfo` 对象)：**
            *   `context_targetIndustry`: String (来自预设行业库)
            *   `context_targetPosition`: String (来自预设职位库或用户自定义)
            *   `context_experienceStage`: String (Enum: "在校生/实习生", "应届毕业生", "职场人士")
            *   `context_yearsOfExperience`: String (Enum: "0年 (在校)", "1年以内", "1-3年", "3-5年", "5-10年", "10年以上")
            *   (其他可选上下文，如期望城市等，可酌情添加)
        *   **用户提示文本示例：**
            *   区域标题：“补充求职信息 (推荐)，AI优化更精准”
            *   引导说明：“提供您的目标行业、职位和经验背景，能帮助AI更好地理解您的需求，给出更专业的优化建议。”

    *   **3.1.3 功能点：触发AI诊断简历**
        *   **功能描述：** 用户在确认“基准简历版本”和（可选的）“上下文信息”后，触发AI对简历进行诊断分析。
        *   **用户流程：** 用户点击“**AI诊断简历**”按钮。
        *   **交互设计：** “AI诊断简历”按钮在用户完成上述输入后变为可用状态。点击后按钮状态变为“AI诊断中…”并伴有加载动画。
        *   **关键字段：** 无新增核心数据字段，此操作将 `baseResume_markdownContent` 和 `userContextInfo` 发送给后端AI服务。

**3.2 阶段二：AI诊断与建议展示 (AI处理 + 用户查阅)**

    *   **3.2.1 功能点：AI简历诊断与建议生成**
        *   **功能描述：** AI（提示词M2-B）对用户提交的“基准简历版本”进行全面诊断分析（可结合用户上下文信息），识别语言表达、成就量化、结构逻辑、关键词匹配等方面的问题和优化点，并生成一份结构化的“诊断报告JSON”。
        *   **用户流程：** (后端AI处理，用户等待)
        *   **交互设计：** 此阶段主要是后端AI处理，前端显示加载状态。
        *   **关键字段 (AI输出 `aiDiagnosisReport_json`，为一个JSON数组，每个对象代表一条建议，包含)：**
            *   `diag_suggestion_id`: String, 唯一ID
            *   `diag_category`: String, 可选 (如：“语言表达优化”, “成就量化建议”, “STAR法则应用”)
            *   `diag_markdown_content`: String (AI生成的、用Markdown排版好的、包含“原文片段”、“存在问题”、“AI建议修改文本”的完整建议描述)
            *   `diag_original_text_for_replacement`: String (纯文本原文片段，供后续AI优化时精确定位和参考)
            *   `diag_suggested_text_for_replacement`: String (纯文本AI建议修改文本，供后续AI优化时参考)
        *   **用户提示文本示例：** (加载提示) “AI正在深度分析您的简历，并生成优化建议报告，请稍候…”

    *   **3.2.2 功能点：诊断报告展示 (供用户参考)**
        *   **功能描述：** 将AI生成的“诊断报告JSON”内容，以清晰、易读的Markdown列表形式展示给用户。此报告仅供用户查阅和理解AI的优化思路，不提供逐条操作按钮。
        *   **用户流程：** AI诊断完成后，前端自动在界面指定区域（如右上）展示诊断报告。
        *   **交互设计：**
            *   右上区域展示一个可滚动的建议列表。
            *   每条建议通过Markdown渲染，清晰呈现“原文片段”、“存在问题”、“AI建议修改文本”。
            *   建议列表上方可有标题，如“AI简历诊断报告 (仅供参考)”。
        *   **关键字段：** (展示 `aiDiagnosisReport_json` 中的 `diag_markdown_content` 列表)
        *   **用户提示文本示例：**
            *   诊断报告区域标题：“AI简历诊断报告 (AI将参考以下建议进行最终优化)”
            *   （若建议较多）“向上滑动查看所有诊断建议。”

**3.3 阶段三：AI自动优化与终稿交付 (AI处理 + 用户确认与导出)**

    *   **3.3.1 功能点：触发AI最终全面优化**
        *   **功能描述：** 用户在查阅完AI诊断报告后（或系统短暂停留后自动引导），触发AI对简历进行最终的全面自动化优化。
        *   **用户流程：** 用户点击“**AI最终优化简历**”按钮（此按钮在诊断报告展示后变为可用）。
        *   **交互设计：** “AI最终优化简历”按钮清晰可见。点击后按钮状态变为“AI优化中…”并伴有加载动画。
        *   **关键字段：** 此操作将 `baseResume_markdownContent`, `userContextInfo`, 以及 `aiDiagnosisReport_json` (或其核心修改指令) 发送给后端AI服务。

    *   **3.3.2 功能点：AI自动全面优化简历**
        *   **功能描述：** AI（提示词M2-C）基于“基准简历版本”，并充分参考“诊断报告JSON”中的所有建议，同时深度结合用户提供的“关键上下文信息”，对简历进行全面的、自动化的修改、整合和润色，生成最终优化版简历。
        *   **用户流程：** (后端AI处理，用户等待)
        *   **交互设计：** 前端显示加载状态。
        *   **关键字段 (AI输出)：**
            *   `aiFinalOptimized_resumeText`: String (AI最终优化后的完整简历文本，纯文本或Markdown)
            *   `aiFinalOptimization_highlights`: String, 可选 (AI对本次优化的主要亮点进行的简短说明，Markdown或文本)
        *   **用户提示文本示例：** (加载提示) “AI正在结合诊断建议和您的求职目标，对您的简历进行最终的全面优化，请稍候…”

    *   **3.3.3 功能点：最终优化结果展示与用户微调**
        *   **功能描述：** 展示AI最终优化后的简历全文，并（可选）附带优化亮点说明。用户可以对结果进行所见即所得的文本微调。
        *   **用户流程：**
            1.  AI优化完成后，前端在界面右下区域以结构化、格式化的方式预览“优化后的完整简历文本”（应用当前选定/默认的内置简历模板）。
            2.  （可选）在预览旁或右上区域展示“优化亮点说明”。
            3.  用户审阅结果，并可直接在预览区进行文本编辑。
        *   **交互设计：**
            *   右下预览区专业美观，支持富文本编辑。
            *   “优化亮点说明”区域清晰易读。
        *   **关键字段：** (展示 `aiFinalOptimized_resumeText` 和 `aiFinalOptimization_highlights`)
        *   **用户提示文本示例：**
            *   优化完成提示：“简历已全面优化！请在下方预览最终效果，您还可以进行手动微调。”
            *   优化亮点说明标题：“本次AI优化主要包括：”

    *   **3.3.4 功能点：“不满意？尝试不同风格/重新生成”**
        *   **功能描述：** 如果用户对AI自动优化的整体结果不满意，可以请求AI尝试不同的优化策略重新生成。
        *   **用户流程：** 用户点击“不满意？尝试不同风格/重新生成”按钮。后端重新调用AI（提示词M2-C，加入不同策略引导或调整参数），并更新前端的“优化后简历预览”和“优化亮点说明”。
        *   **交互设计：** 按钮位于最终预览结果的显著位置。点击后有加载提示。
        *   **关键字段：** (触发对提示词M2-C的再次调用)
        *   **用户提示文本示例：** 按钮文本：“换个优化方案试试” 或 “AI再优化一次 (不同风格)”

    *   **3.3.5 功能点：导出与（V1暂无）保存简历**
        *   **功能描述：** 用户将最终确认的优化简历导出为本地文件。V1阶段不提供云端保存。
        *   **用户流程：** 用户对最终结果满意后，点击“导出简历”按钮，选择格式并下载。
        *   **交互设计：** “导出简历”按钮清晰可见。导出格式选择明确。
        *   **关键字段：** (参考通用功能中的导出设置)
        *   **用户提示文本示例：** (同模块1的导出提示)

---



---

**模块3：AI仿写优质简历 - 详细功能需求**

**1. 功能模块总览**

*   **模块名称：** AI仿写优质简历
*   **模块ID：** M3
*   **模块入口：** 网站主导航栏“AI仿写”链接/按钮，或首页引导入口。
*   **核心目标：** 允许用户提供一份其认可的“参考优质简历”范例，并结合用户自身的“基准简历”内容。AI深度分析参考范例的结构、风格、表达等优点，向用户展示分析报告摘要并获得确认后，智能地将这些优点“嫁接”或“适配”到用户的基准简历上，生成一份既保留用户真实信息、又具备范例亮点的优化简历。

**2. 整体界面与交互原则**

*   **2.1 界面布局：**
    *   **描述：** 采用“优化的左右分栏”布局。
        *   **左侧区域：** 用户“基准简历”内容导入/编辑区，“参考优质简历”内容导入/确认区，（推荐的）用户上下文信息输入区，以及阶段性操作按钮。
        *   **右侧区域：** 根据当前流程步骤动态展示内容。
            *   **参考简历分析阶段：** 展示AI对“参考优质简历”的分析报告摘要供用户确认。
            *   **仿写结果阶段：** 展示AI最终仿写优化后的简历全文预览，并（可选）附带“仿写亮点说明”。
    *   **交互：** 用户在左侧完成输入和触发操作，右侧相应区域展示AI的处理结果或需要用户确认的信息。

*   **2.2 交互原则：**
    *   **分步清晰：** 通过明确的按钮和界面状态，引导用户完成“提供自身简历 -> 提供参考简历 -> 分析参考简历并确认 -> AI执行仿写 -> 结果预览与交付”的流程。
    *   **用户参与决策：** 用户在AI正式仿写前，有机会预览并确认AI对参考简历的分析理解，增强掌控感和仿写方向的准确性。
    *   **保持真实性：** AI仿写过程严格基于用户提供的真实信息，避免虚构。
    *   **结果可控：** 用户对AI仿写生成的最终简历拥有微调权和请求“再仿写一次”的权利。

**3. 详细功能点、字段、流程与交互**

**3.1 阶段一：用户输入与准备 (用户操作)**

    *   **3.1.1 功能点：用户“基准简历”提供与确认**
        *   **功能描述：** 用户通过粘贴文本、上传文档或（V1暂无，未来可考虑）选择平台已保存简历的方式，提供其自身的简历内容。系统（AI辅助）对上传的文档进行解析和Markdown格式化处理，用户在富文本编辑器中对导入内容进行最终确认，形成“用户基准简历”。
        *   **用户流程：**
            1.  用户进入“AI仿写优质简历”模块。
            2.  左侧“我的简历内容”区域提示用户选择导入方式。
            3.  **若选择“粘贴文本”：** 用户将简历内容粘贴到支持Markdown的富文本编辑器中。
            4.  **若选择“上传文档” (.doc, .docx, .pdf)：** 用户上传文件。后端调用AI（提示词M3-A1：“文档解析与Markdown化”）进行文档解析与Markdown化，结果回显到富文本编辑器。
            5.  用户在富文本编辑器中审阅并（可）编辑内容，最终确认形成“**userBaseResume_markdownContent**”。
        *   **交互设计：**
            *   左侧清晰划分出“我的简历内容”区域。
            *   提供“粘贴简历内容”文本区域和“上传简历文件”按钮。
            *   上传文档后有加载提示，Markdown内容自动填充到编辑器。
            *   富文本编辑器提供基础的Markdown编辑和预览功能。
        *   **关键字段：**
            *   `userInput_baseResumeFile`: File Object (用户上传的基准简历文件，临时)
            *   `userInput_baseResumePastedText`: String (用户粘贴的基准简历文本)
            *   `userBaseResume_markdownContent`: String (用户最终确认的、作为仿写基础的简历Markdown文本)
        *   **用户提示文本示例：**
            *   区域标题：“第一步：提供您自己的简历内容”
            *   引导文本：“请粘贴或上传您现有的简历，AI将以此为基础进行仿写。”

    *   **3.1.2 功能点：用户上下文信息补充 (推荐)**
        *   **功能描述：** 引导用户提供其求职意向和职场画像等上下文信息，供AI在仿写时进行更智能的适配。
        *   **用户流程：** 在用户确认“用户基准简历”后（或并行），左侧出现上下文信息填写表单。用户填写后信息暂存。
        *   **交互设计：** 同模块2的3.1.2。
        *   **关键字段 (存储为 `userContextInfo` 对象)：** 同模块2的3.1.2。
        *   **用户提示文本示例：** 同模块2的3.1.2。

    *   **3.1.3 功能点：“参考优质简历”提供与确认**
        *   **功能描述：** 用户通过粘贴文本或上传文档的方式，提供一份其认可的“参考优质简历”范例。系统（AI辅助）对上传的文档进行解析和Markdown格式化处理，供用户确认参考内容。
        *   **用户流程：**
            1.  在用户提供完“用户基准简历”和（可选的）上下文信息后，左侧出现“参考优质简历”输入区。
            2.  用户选择导入方式。
            3.  **若选择“粘贴文本”：** 用户将参考简历内容粘贴到指定的文本区域（可以是普通文本区或Markdown编辑器）。
            4.  **若选择“上传文档”：** 用户上传文件。后端调用AI（提示词M3-A2：“文档解析与Markdown化”）进行处理，结果回显到参考简历的预览/编辑区。
            5.  用户审阅并（可）编辑参考简历的文本内容，最终确认形成“**referenceResume_markdownContent**”。
        *   **交互设计：**
            *   左侧清晰划分出“参考优质简历内容”区域。
            *   提供与“用户基准简历”类似的粘贴/上传和编辑器交互。
        *   **关键字段：**
            *   `userInput_refResumeFile`: File Object (用户上传的参考简历文件，临时)
            *   `userInput_refResumePastedText`: String (用户粘贴的参考简历文本)
            *   `referenceResume_markdownContent`: String (用户最终确认的参考简历Markdown文本)
        *   **用户提示文本示例：**
            *   区域标题：“第二步：提供您参考的优质简历范例”
            *   引导文本：“请粘贴或上传一份您认为优秀的简历作为AI学习的范本。”

    *   **3.1.4 功能点：触发AI分析参考简历**
        *   **功能描述：** 用户在确认“用户基准简历”、“上下文信息”（可选）和“参考优质简历”后，触发AI对“参考优质简历”进行特征分析。
        *   **用户流程：** 用户点击“**AI分析参考简历**”按钮。
        *   **交互设计：** 按钮在所有必要输入完成后变为可用。点击后按钮状态变为“AI分析中…”并伴有加载动画。
        *   **关键字段：** 此操作将 `referenceResume_markdownContent` 发送给后端AI服务。

**3.2 阶段二：AI分析参考简历并向用户展示分析结果 (AI处理 + 用户确认)**

    *   **3.2.1 功能点：AI分析参考简历特征**
        *   **功能描述：** AI（提示词M3-B）对用户提交的“参考优质简历”进行深度分析，提取其结构、语言风格、表达技巧、内容呈现等关键特征，并生成一份结构化的“参考简历特征分析报告”（内部为JSON）。
        *   **用户流程：** (后端AI处理，用户等待)
        *   **交互设计：** 前端显示加载状态。
        *   **关键字段 (AI输出 `aiReferenceAnalysis_report_json`，内部JSON结构，包含但不限于)：**
            *   `ref_identifiedStructure`: Array of Strings (识别出的模块顺序和名称)
            *   `ref_languageStyle`: Array of Strings (识别出的语言风格标签，如：“简洁”、“结果导向”、“使用强动词”)
            *   `ref_expressionTechniques`: Array of Strings (识别出的表达技巧，如：“STAR法则应用”、“成就量化突出”)
            *   `ref_keyActionVerbs`: Array of Strings (常用的强动词示例)
            *   `ref_industryOrPositionClues`: String (对参考简历可能适用的行业/岗位的推断)
        *   **用户提示文本示例：** (加载提示) “AI正在深度解读您提供的参考简历，分析其亮点与特色…”

    *   **3.2.2 功能点：展示参考简历分析报告摘要并获取用户确认**
        *   **功能描述：** 将“参考简历特征分析报告”的核心内容或摘要，以用户易于理解的方式展示出来，供用户审阅并确认AI的分析方向基本准确，然后才进行正式的仿写。
        *   **用户流程：**
            1.  AI分析完成后，前端在界面指定区域（如右侧临时展示区或模态框）展示分析报告摘要。
            2.  用户阅读摘要，了解AI对参考简历的主要理解。
            3.  用户点击“**确认分析，并开始AI仿写**”按钮以继续。
            4.  若用户认为AI分析偏差较大，可选择“返回修改参考简历”或“重新分析参考简历”。
        *   **交互设计：**
            *   分析报告摘要展示应简洁明了，突出AI的关键发现（例如，用标签云、列表、简短描述等）。
            *   “确认分析，并开始AI仿写”按钮清晰可见。
            *   提供返回修改或重新分析的路径。
        *   **关键字段：**
            *   `aiReferenceAnalysis_summaryForUser`: String (由AI生成或后端根据JSON报告整理的、展示给用户的分析摘要文本/Markdown)
        *   **用户提示文本示例：**
            *   摘要区域标题：“AI对参考简历的分析概要”
            *   引导文本：“AI已分析出参考简历的主要特点如下。请确认后，AI将尝试将这些优点应用到您的简历中。”
            *   确认按钮文本：“是的，按此分析开始仿写”
            *   返回选项：“参考简历内容有误，返回修改” 或 “让AI重新分析一次参考简历”

**3.3 阶段三：AI执行仿写 (AI处理，在用户确认后)**

    *   **3.3.1 功能点：AI智能仿写与内容适配**
        *   **功能描述：** AI（提示词M3-C）基于“用户基准简历”，并充分参考（经用户确认的）“参考简历特征分析报告”，同时结合（可选的）用户“关键上下文信息”，智能地将参考简历的优点（结构、风格、表达方式等）创造性地“嫁接”或“适配”到用户基准简历的内容上，生成一份新的简历版本。强调保持用户核心信息的真实性。
        *   **用户流程：** 用户点击“确认分析，并开始AI仿写”后，后端AI执行此操作，用户等待。
        *   **交互设计：** 前端显示加载状态。
        *   **关键字段 (AI输出)：**
            *   `aiImitated_resumeText`: String (AI仿写优化后的完整简历文本，纯文本或Markdown)
            *   `aiImitation_highlights`: String, 可选 (AI对本次仿写的主要亮点和借鉴点进行的简短说明，Markdown或文本)
        *   **用户提示文本示例：** (加载提示) “AI正在努力学习参考简历的精华，并为您量身打造一份融合其优点的新简历，请稍候…”

**3.4 阶段四：结果展示与交付 (用户确认与导出)**

    *   **3.4.1 功能点：仿写结果与说明展示**
        *   **功能描述：** 展示AI仿写生成的简历初稿，并（可选）附带仿写亮点说明。
        *   **用户流程：** AI仿写完成后，前端在界面右侧预览区展示结果。
        *   **交互设计：**
            *   右侧预览区以结构化、格式化的方式展示“仿写优化后的简历初稿”（应用当前选定/默认的内置简历模板）。
            *   （推荐）在预览旁或右上区域展示“本次仿写亮点说明”。
        *   **关键字段：** (展示 `aiImitated_resumeText` 和 `aiImitation_highlights`)
        *   **用户提示文本示例：**
            *   仿写完成提示：“简历仿写已完成！请在右侧预览AI为您生成的融合了范例优点的新版简历。”
            *   仿写亮点说明标题：“本次AI仿写主要借鉴并应用了参考简历的以下特点：”

    *   **3.4.2 功能点：用户微调与“不满意？尝试不同仿写策略/重新生成”**
        *   **功能描述：** 用户可以对AI仿写的简历进行所见即所得的文本微调。如果对整体仿写效果不满意，可以请求AI尝试不同的仿写策略重新生成。
        *   **用户流程：**
            1.  用户审阅仿写结果和说明。
            2.  直接在右侧预览区进行文本编辑。
            3.  若不满意，点击“不满意？尝试不同仿写策略/重新生成”按钮。后端重新调用AI（提示词M3-C，加入不同仿写侧重引导或调整参数），并更新前端的预览结果和说明。
        *   **交互设计：**
            *   右侧预览区支持富文本编辑。
            *   “不满意？尝试不同仿写策略/重新生成”按钮位于预览结果的显著位置。点击后有加载提示。
        *   **关键字段：** (触发对提示词M3-C的再次调用，可能需要传递额外的策略参数)
        *   **用户提示文本示例：**
            *   按钮文本：“换个仿写思路试试” 或 “AI再仿写一次 (调整侧重)”
            *   策略选择提示 (如果支持用户选择仿写策略)：“您希望AI在重新仿写时更侧重于：[A. 结构模仿 B. 语言风格学习 C. 表达技巧借鉴]？”

    *   **3.4.3 功能点：导出与（V1暂无）保存简历**
        *   **功能描述：** 用户将最终确认的仿写简历导出为本地文件。V1阶段不提供云端保存。
        *   **用户流程 & 交互设计 & 关键字段 & 用户提示文本：** (同模块1和模块2的导出功能)

---


---

**模块4：AI简历精准匹配JD - 详细功能需求**

**1. 功能模块总览**

*   **模块名称：** AI简历精准匹配JD
*   **模块ID：** M4
*   **模块入口：** 网站主导航栏“AI匹配JD”链接/按钮，或首页引导入口。
*   **核心目标：** 帮助用户将其现有简历与特定的职位描述（JD）进行深度匹配和针对性优化。AI首先分析用户提供的JD，提取核心要求与关键词并向用户展示分析摘要供确认，然后结合用户的“基准简历”，智能地对简历内容进行调整、强化和改写，以最大化简历与目标JD的契合度，最终交付一份高度匹配的优化简历。

**2. 整体界面与交互原则**

*   **2.1 界面布局：**
    *   **描述：** 采用“优化的左右分栏”布局。
        *   **左侧区域：** 用户目标JD输入区，“用户基准简历”内容导入/编辑区，以及阶段性操作按钮。
        *   **右侧区域：** 根据当前流程步骤动态展示内容。
            *   **JD分析阶段：** 展示AI对目标JD的分析报告摘要供用户确认。
            *   **匹配优化结果阶段：** 展示AI最终针对JD优化后的简历全文预览，并（可选）附带“简历JD匹配度与优化说明”报告。
    *   **交互：** 用户在左侧完成输入和触发操作，右侧相应区域展示AI的处理结果或需要用户确认的信息。

*   **2.2 交互原则：**
    *   **分步引导清晰：** 引导用户完成“提供JD -> 分析JD并确认 -> 提供自身简历 -> AI执行匹配优化 -> 结果预览与交付”的流程。
    *   **JD理解透明化：** 用户在AI正式进行简历匹配优化前，有机会预览并确认AI对JD核心要求的理解。
    *   **精准匹配为核心：** AI的所有优化操作都以提升简历与目标JD的匹配度为首要目标。
    *   **保持真实性与可读性：** AI改写时需在匹配JD与保持用户简历信息真实、行文自然之间取得平衡。
    *   **结果可控：** 用户对AI生成的优化简历拥有微调权和请求“再优化一次”的权利。

**3. 详细功能点、字段、流程与交互**

**3.1 阶段一：用户输入与JD分析确认 (用户操作 + AI处理 + 用户确认)**

    *   **3.1.1 功能点：目标职位描述 (JD) 输入**
        *   **功能描述：** 用户提供其目标职位的职位描述（JD）文本内容。
        *   **用户流程：**
            1.  用户进入“AI简历精准匹配JD”模块。
            2.  左侧“目标职位描述 (JD)”区域提示用户粘贴JD文本。
            3.  用户将JD文本内容粘贴到指定的文本区域。
        *   **交互设计：** 左侧提供一个清晰的多行文本输入框用于粘贴JD。
        *   **关键字段：**
            *   `userInput_jobDescriptionText`: String, 必填 (用户粘贴的JD原文)
        *   **用户提示文本示例：**
            *   区域标题：“第一步：提供您要匹配的目标职位描述 (JD)”
            *   输入框占位符：“在此粘贴完整的职位描述内容…”
            *   引导提示：“请确保粘贴的JD内容尽可能详细，这将直接影响AI优化的精准度。”

    *   **3.1.2 功能点：触发AI分析JD**
        *   **功能描述：** 用户在粘贴完JD后，触发AI对JD进行深度解析。
        *   **用户流程：** 用户点击“**AI分析此JD**”按钮。
        *   **交互设计：** 按钮在用户输入JD后变为可用。点击后按钮状态变为“AI分析中…”并伴有加载动画。
        *   **关键字段：** 此操作将 `userInput_jobDescriptionText` 发送给后端AI服务。

    *   **3.1.3 功能点：AI分析JD并生成报告**
        *   **功能描述：** AI（提示词M4-A）对用户提交的JD文本进行全面、深度的分析，提取核心职责、技能要求（硬/软）、经验要求、学历要求、高频关键词、行业术语等，并生成一份结构化的“JD画像分析报告”（内部为JSON）。
        *   **用户流程：** (后端AI处理，用户等待)
        *   **交互设计：** 前端显示加载状态。
        *   **关键字段 (AI输出 `aiJobDescriptionAnalysis_report_json`，内部JSON结构，包含但不限于)：**
            *   `jd_identifiedPositionName`: String (识别出的职位名称)
            *   `jd_identifiedCompanyName`: String (识别出的公司名称，若有)
            *   `jd_coreResponsibilities`: Array of Strings (核心职责列表)
            *   `jd_hardSkillsRequired`: Array of Strings (硬技能要求列表)
            *   `jd_softSkillsRequired`: Array of Strings (软技能要求列表)
            *   `jd_experienceRequirements`: String (经验要求描述)
            *   `jd_educationRequirements`: String (学历要求描述)
            *   `jd_highFrequencyKeywords`: Array of Strings (高频关键词列表)
        *   **用户提示文本示例：** (加载提示) “AI正在深度解读您提供的职位描述，分析其核心要求…”

    *   **3.1.4 功能点：展示JD分析报告摘要并获取用户确认**
        *   **功能描述：** 将“JD画像分析报告”的核心内容或摘要，以用户易于理解的方式展示出来，供用户审阅并确认AI对JD的理解基本准确，然后才继续提供用户简历。
        *   **用户流程：**
            1.  AI分析JD完成后，前端在界面指定区域（如右侧临时展示区或模态框）展示分析报告摘要。
            2.  用户阅读摘要，了解AI对JD的主要理解。
            3.  用户点击“**确认JD分析，并提供我的简历**”按钮以继续。
            4.  若用户认为AI分析偏差较大，可选择“返回修改JD内容”或“让AI重新分析JD”。
        *   **交互设计：**
            *   分析报告摘要展示应简洁明了，突出AI的关键发现（例如，职位名称、核心技能、经验年限等）。
            *   “确认JD分析，并提供我的简历”按钮清晰可见。
            *   提供返回修改或重新分析的路径。
        *   **关键字段：**
            *   `aiJobDescriptionAnalysis_summaryForUser`: String (由AI生成或后端根据JSON报告整理的、展示给用户的JD分析摘要文本/Markdown)
        *   **用户提示文本示例：**
            *   摘要区域标题：“AI对职位描述的分析概要”
            *   引导文本：“AI已分析出该职位的主要要求如下。请确认后，以便AI能更精准地优化您的简历。”
            *   确认按钮文本：“是的，JD分析准确，继续提供我的简历”
            *   返回选项：“JD内容有误/不准确，返回修改”

    *   **3.1.5 功能点：用户“基准简历”提供与确认**
        *   **功能描述：** 在用户确认JD分析结果后，引导用户通过粘贴文本、上传文档或（V1暂无）选择平台已保存简历的方式，提供其自身的简历内容。系统（AI辅助）对上传的文档进行解析和Markdown格式化处理，用户在富文本编辑器中对导入内容进行最终确认，形成“用户基准简历”。
        *   **用户流程 & 交互设计 & 关键字段：** (同模块3的3.1.1，但区域标题和引导文本需适配模块4场景)
            *   `userBaseResume_markdownContent`: String (用户最终确认的、用于JD匹配的基准简历Markdown文本)
        *   **用户提示文本示例：**
            *   区域标题：“第二步：提供您自己的简历内容”
            *   引导文本：“请粘贴或上传您现有的简历，AI将根据之前分析的JD对其进行匹配和优化。”

    *   **3.1.6 功能点：触发AI匹配并优化简历**
        *   **功能描述：** 用户在确认“用户基准简历”后，触发AI执行简历与JD的匹配分析和针对性改写。
        *   **用户流程：** 用户点击“**开始AI匹配并优化简历**”按钮。
        *   **交互设计：** 按钮在用户确认基准简历后变为可用。点击后按钮状态变为“AI匹配优化中…”并伴有加载动画。
        *   **关键字段：** 此操作将 `userBaseResume_markdownContent` 和 `aiJobDescriptionAnalysis_report_json` (或其核心指令) 发送给后端AI服务。

**3.2 阶段二：AI执行JD匹配、简历改写与结果展示 (AI处理 + 用户查阅与交互)**

    *   **3.2.1 功能点：AI执行JD匹配与简历智能改写**
        *   **功能描述：** AI（提示词M4-B）基于“用户基准简历”和（经用户确认的）“JD画像分析报告”，进行全面的对比分析，并智能地对用户简历进行针对性的修改、强化、关键词融入、内容侧重调整等，以最大化与目标JD的匹配度，同时保持用户信息真实性。
        *   **用户流程：** (后端AI处理，用户等待)
        *   **交互设计：** 前端显示加载状态。
        *   **关键字段 (AI输出)：**
            *   `aiJdMatched_resumeText`: String (AI针对JD优化后的完整简历文本，纯文本或Markdown)
            *   `aiJdMatch_optimizationReport`: String, 可选但推荐 (AI对本次匹配优化的主要说明、匹配亮点、以及可能的差距提示，Markdown或文本)
        *   **用户提示文本示例：** (加载提示) “AI正在全力分析您的简历与职位要求的匹配度，并进行针对性优化，请稍候…”

    *   **3.2.2 功能点：匹配优化结果与说明展示**
        *   **功能描述：** 展示AI针对JD优化后的简历全文，并（推荐）附带匹配度与优化说明报告。
        *   **用户流程：** AI处理完成后，前端在界面右侧预览区展示结果。
        *   **交互设计：**
            *   右侧预览区以结构化、格式化的方式展示“优化后的完整简历文本”（应用当前选定/默认的内置简历模板）。
            *   （推荐）在预览旁或右上独立区域展示“简历JD匹配度与优化说明”报告。
        *   **关键字段：** (展示 `aiJdMatched_resumeText` 和 `aiJdMatch_optimizationReport`)
        *   **用户提示文本示例：**
            *   优化完成提示：“简历已针对目标职位优化完成！请在右侧预览效果，并查看AI的优化说明。”
            *   优化说明报告标题：“简历与JD匹配优化报告：”

    *   **3.2.3 功能点：用户微调与“不满意？尝试不同匹配策略/重新生成”**
        *   **功能描述：** 用户可以对AI优化的简历进行所见即所得的文本微调。如果对整体匹配优化效果不满意，可以请求AI尝试不同的匹配策略重新生成。
        *   **用户流程 & 交互设计 & 关键字段 & 用户提示文本：** (同模块3的3.4.2，但“AI再优化一次”的策略引导需针对JD匹配场景，例如“更侧重技能关键词匹配”、“更突出相关项目经验”等)

    *   **3.2.4 功能点：导出与（V1暂无）保存简历**
        *   **功能描述 & 用户流程 & 交互设计 & 关键字段 & 用户提示文本：** (同模块1和模块2的导出功能)

---

