# AI智能简历助手 - 技术栈设计

## 1. 前端技术栈

- **核心框架**: React 18
- **UI组件库**: Ant Design (antd) 5.x
- **状态管理**: Redux Toolkit
- **路由管理**: React Router v6
- **表单处理**: Formik + Yup (表单验证)
- **HTTP客户端**: Axios
- **文件处理**:
  - react-pdf (PDF预览与生成)
  - docx (Word文档生成)
  - file-saver (文件下载)
- **富文本编辑**: React Quill / Draft.js (支持Markdown)
- **样式解决方案**: 
  - Tailwind CSS (实用工具类)
  - Styled Components (组件样式隔离)
- **构建工具**: Vite (比Create React App更快的构建速度)

## 2. 后端技术栈

- **运行环境**: Node.js 18+ LTS
- **Web框架**: Express.js
- **API文档**: Swagger/OpenAPI
- **文件解析**:
  - pdf-parse (解析PDF文件)
  - mammoth (解析Word文档)
- **安全**: 
  - helmet (HTTP头安全)
  - express-rate-limit (请求限制)
  - cors (跨域资源共享)
- **日志**: Winston / Morgan
- **验证**: Joi (请求数据验证)

## 3. 数据库

- **主数据库**: MySQL (云端部署)
- **ORM**: Sequelize (简化数据库操作)
- **临时存储**: 
  - 会话数据: Express-session + session-file-store
  - 浏览器存储: localStorage/sessionStorage (客户端临时数据)

## 4. AI/LLM集成

- **LLM模型**: Google Gemini API
- **AI接口封装**: 自定义Gemini API服务封装层
- **提示词管理**: 集中式提示词模板系统
  - M1系列: 简历创建相关提示词
  - M2系列: 简历优化相关提示词
  - M3系列: 简历仿写相关提示词
  - M4系列: JD匹配相关提示词

## 5. 核心功能模块技术实现

### 5.1 通用组件

- **简历模板系统**: 基于React组件的可切换模板
- **文档解析服务**: 多格式文档解析为统一JSON/Markdown格式
- **导出服务**: PDF/Word/TXT多格式导出

### 5.2 模块1: AI代写简历

- 分步表单 (Step Form) 组件
- 实时预览渲染组件
- Formik表单状态管理
- AI生成内容接口封装

### 5.3 模块2: AI改写简历

- 文档上传/粘贴组件
- AI诊断报告渲染组件
- 文档对比组件 (改写前后)

### 5.4 模块3: AI仿写优质简历

- 双文档处理组件
- 特征分析展示组件
- 仿写结果预览组件

### 5.5 模块4: AI简历精准匹配JD

- JD分析组件
- 匹配度可视化组件
- 关键词突显组件

## 6. 部署与构建

- **前端构建**: Vite生产构建
- **后端部署**: Node.js + PM2
- **API代理**: Nginx
- **CI/CD**: 暂不考虑Docker部署，后续可集成

## 7. 性能与优化考虑

- 组件懒加载
- 代码分割
- 图片优化
- AI请求队列管理
- 缓存策略

## 8. 扩展性考虑

该技术栈设计支持未来V2版本可能增加的功能:
- 用户账户系统
- 简历库管理
- 付费功能
- 更多模板
- 多设备同步

## 9. 文件结构建议

```
/
├── client/                # 前端React项目
│   ├── public/
│   ├── src/
│   │   ├── components/    # 共享组件
│   │   ├── modules/       # 按功能模块组织的组件
│   │   │   ├── home/      # 首页
│   │   │   ├── module1/   # AI代写简历
│   │   │   ├── module2/   # AI改写简历
│   │   │   ├── module3/   # AI仿写优质简历
│   │   │   └── module4/   # AI简历精准匹配JD
│   │   ├── services/      # API服务
│   │   ├── utils/         # 工具函数
│   │   ├── templates/     # 简历模板
│   │   ├── store/         # Redux状态
│   │   ├── App.jsx
│   │   └── main.jsx
│   └── package.json
│
├── server/                # 后端Express项目
│   ├── src/
│   │   ├── controllers/   # 控制器
│   │   ├── models/        # 数据模型(Sequelize)
│   │   ├── routes/        # 路由
│   │   ├── services/      # 业务逻辑
│   │   ├── utils/         # 工具函数
│   │   ├── ai/            # AI服务相关
│   │   │   ├── gemini.js  # Gemini API封装
│   │   │   └── prompts/   # 提示词模板
│   │   ├── middlewares/   # 中间件
│   │   └── app.js         # 主应用
│   └── package.json
│
└── README.md
``` 